using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.GpPractices
{
    public class DeleteModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DeleteModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int GpPracticeId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string StreetLineOne { get; set; } = string.Empty;
        public string? StreetLineTwo { get; set; }
        public string City { get; set; } = string.Empty;
        public string Postcode { get; set; } = string.Empty;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var gpPractice = await _context.GpPractices
                .Where(g => g.Id == id && g.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (gpPractice == null)
            {
                return NotFound();
            }

            GpPracticeId = gpPractice.Id;
            Name = gpPractice.Name;
            StreetLineOne = gpPractice.StreetLineOne;
            StreetLineTwo = gpPractice.StreetLineTwo;
            City = gpPractice.City;
            Postcode = gpPractice.Postcode;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var gpPractice = await _context.GpPractices
                .Where(g => g.Id == GpPracticeId && g.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (gpPractice == null)
            {
                return NotFound();
            }

            _context.GpPractices.Remove(gpPractice);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "GP practice deleted successfully";

            return RedirectToPage("./Index");
        }
    }
}
