@page
@model Life.Pages.DrivingLicences.IndexModel
@{
    ViewData["Title"] = "Driving Licences";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Driving Licences</h2>
                    <a asp-page="./Create" class="btn btn-sm btn-outline-success title-button">
                        <i class="bi bi-plus-square"></i>
                    </a>
                </div>
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
					</div>
                }

                @if (Model.DrivingLicences.Any())
                {
                    <div class="card-body">
                        <h5 class="card-title">Current Driving Licences</h5>

                        <div class="list-group">
                            @foreach (var licence in Model.DrivingLicences)
                            {
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between my-3">
                                        <div>
                                            <h5 class="mb-2 text-break">@licence.Name</h5>
                                            <h6>Expiry Date: @licence.ExpiryDate.ToString("dd/MM/yyyy")</h6>
                                            <h6>Photocard Expires: @licence.PhotocardExpiryDate.ToString("dd/MM/yyyy")</h6>
                                            <div class="mt-2">
                                                <a asp-page="./Details" asp-route-id="@licence.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Details
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@licence.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                } 
                else
                {
                    <div class="card-body">
                        <h5 class="card-title">Current Driving Licences</h5>
                        <p>There are no current licences</p>
                    </div>
                }


                @if (Model.ExpiredDrivingLicences.Any())
                {
                    <hr class="mt-5" />
                    <div class="card-body expired-items">
                        <h5 class="card-title">Expired Driving Licences</h5>

                        <div class="list-group">
                            @foreach (var licence in Model.ExpiredDrivingLicences)
                            {
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between my-3">
                                        <div>
                                            <h5 class="mb-2 text-break">@licence.Name</h5>
                                            @if (licence.ExpiryDate < DateOnly.FromDateTime(DateTime.Now))
                                            {
                                                <h6 class="expired-date">Expired: @licence.ExpiryDate.ToString("dd/MM/yyyy")</h6>
                                            }
                                            else
                                            {
                                                <h6>Expiry Date: @licence.ExpiryDate.ToString("dd/MM/yyyy")</h6>
											}
                                            <h6 class="expired-date">Photocard Expired: @licence.PhotocardExpiryDate.ToString("dd/MM/yyyy")</h6>
                                            <div class="mt-2">
                                                <a asp-page="./Details" asp-route-id="@licence.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Details
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@licence.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
