@page
@model Life.Pages.GpPractices.EditModel
@{
    ViewData["Title"] = "Edit GP Practice";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Edit GP Practice</h2>
                </div>
                <div class="card-body">
                    <form method="post" class="row g-3 mt-1" autocomplete="off">
						<input type="hidden" asp-for="GpPracticeId" />

                        <div class="row my-3">
                            <small class="text-muted">Required fields are marked in <strong>bold</strong>.</small>
                        </div>

                        <div class="row my-3">
                            <label asp-for="Name" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="Name" class="form-control" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="StreetLineOne" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="StreetLineOne" class="form-control" />
                                <span asp-validation-for="StreetLineOne" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="StreetLineTwo" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="StreetLineTwo" class="form-control" />
                                <span asp-validation-for="StreetLineTwo" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="City" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="City" class="form-control" />
                                <span asp-validation-for="City" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="Postcode" class="col-sm-2 col-form-label required-field"></label>
                            <div class="col-sm-10">
                                <input asp-for="Postcode" class="form-control" />
                                <span asp-validation-for="Postcode" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="PhoneNumber" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="PhoneNumber" class="form-control" />
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="Website" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <input asp-for="Website" class="form-control" />
                                <span asp-validation-for="Website" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <label asp-for="AddressId" class="col-sm-2 col-form-label"></label>
                            <div class="col-sm-10">
                                <select asp-for="AddressId" asp-items="Model.Addresses" class="form-select">
                                    <option value="">Select an address (optional)</option>
                                </select>
                                <span asp-validation-for="AddressId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row my-3">
                            <div class="col-sm-10 offset-sm-2">
                                <button type="submit" class="btn btn-sm btn-primary">Save</button>
                                <a asp-page="./Details" asp-route-id="@Model.GpPracticeId" class="btn btn-sm btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
