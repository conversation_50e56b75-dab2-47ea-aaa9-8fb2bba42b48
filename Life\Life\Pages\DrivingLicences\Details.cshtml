@page
@model Life.Pages.DrivingLicences.DetailsModel
@{
    ViewData["Title"] = "Driving Licence Details";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2 class="mb-0">Driving Licence Details</h2>
                    <a asp-page="./Index" class="btn btn-sm btn-outline-primary title-button">
                        <i class="bi bi-list"></i>
                    </a>
                </div>
                <div class="card-body">

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                            <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                                @TempData["SuccessMessage"]
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        </div>
                    }

                    <div class="row mt-3">
                        <div class="col-md-8">
                            <h4 class="card-title">@Model.Name</h4>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Licence Number:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.LicenceNumber))
                                    {
                                        @Model.LicenceNumber
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Start Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.StartDate.ToString("dd/MM/yyyy")
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Expiry Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="@(Model.ExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow)
                                        ? "text-danger"
                                        : Model.ExpiryDate == DateOnly.FromDateTime(DateTime.UtcNow)
                                            ? "text-danger"
                                            : Model.ExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(6))
                                                ? "text-warning"
                                                : "text-success")">
                                        @Model.ExpiryDate.ToString("dd/MM/yyyy")
                                        @if (Model.ExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow))
                                        {
                                            <span class="badge bg-danger ms-2">Expired</span>
                                        }
                                        else if (Model.ExpiryDate == DateOnly.FromDateTime(DateTime.UtcNow))
                                        {
                                            <span class="badge bg-danger ms-2">Expires Today</span>

                                        }
                                        else if (Model.ExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(6)))
                                        {
                                            <span class="badge bg-warning ms-2">Expires Soon</span>
                                        }
                                    </span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Photocard Start Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.PhotocardStartDate.ToString("dd/MM/yyyy")
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Photocard Expiry Date:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="@(Model.PhotocardExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow)
                                        ? "text-danger"
                                        : Model.PhotocardExpiryDate == DateOnly.FromDateTime(DateTime.UtcNow)
                                            ? "text-danger"
                                            : Model.PhotocardExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(6))
                                                ? "text-warning"
                                                : "text-success")">
                                        @Model.PhotocardExpiryDate.ToString("dd/MM/yyyy")
                                        @if (Model.PhotocardExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow))
                                        {
                                            <span class="badge bg-danger ms-2">Expired</span>
                                        }
                                        else if (Model.PhotocardExpiryDate == DateOnly.FromDateTime(DateTime.UtcNow))
                                        {
                                            <span class="badge bg-danger ms-2">Expires Today</span>

                                        }
                                        else if (Model.PhotocardExpiryDate < DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(6)))
                                        {
                                            <span class="badge bg-warning ms-2">Expires Soon</span>
                                        }
                                    </span>
                                </div>
                            </div>

                            
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Address:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (!string.IsNullOrEmpty(Model.AddressDisplay))
                                    {
                                        <a asp-page="/Addresses/Details" asp-route-id="@Model.AddressId" class="link-primary">
                                            @Model.AddressDisplay
                                        </a>
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Notes:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @if (!string.IsNullOrWhiteSpace(Model.Notes))
                                    {
                                        <div style="white-space: pre-wrap;">@Model.Notes</div>
                                    }
                                    else
                                    {
                                        @:Not Set
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Created:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong>Last Updated:</strong>
                                </div>
                                <div class="col-sm-8">
                                    @Model.UpdatedAt.ToString("dd/MM/yyyy HH:mm")
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="bi bi-bell"></i> Reminder Status
                                    </h6>
                                    @if (Model.HasLinkedReminders)
                                    {
                                        <p class="text-success">
                                            <i class="bi bi-check-circle"></i>
                                            Automatic reminders are set
                                        </p>
                                    }
                                    else
                                    {
                                        <p class="text-warning">
                                            <i class="bi bi-exclamation-triangle"></i>
                                            No automatic reminders found
                                        </p>
                                    }
                                </div>
                            </div>

                            <div class="card bg-light mt-3">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="bi bi-calendar"></i> Time Remaining
                                    </h6>
                                    @{
                                        var today = DateOnly.FromDateTime(DateTime.UtcNow);
                                        var daysUntilExpiry = (Model.ExpiryDate.DayNumber - today.DayNumber);
                                        var daysUntilPhotocardExpiry = (Model.PhotocardExpiryDate.DayNumber - today.DayNumber);
                                        var earliestExpiry = Math.Min(daysUntilExpiry, daysUntilPhotocardExpiry);
                                    }
                                    @if (earliestExpiry < 0)
                                    {
                                        <p class="text-danger">
                                            @if (daysUntilExpiry < 0 && daysUntilPhotocardExpiry < 0)
                                            {
                                                @:Both expired
                                            }
                                            else if (daysUntilExpiry < 0)
                                            {
                                                @:Licence expired @Math.Abs(daysUntilExpiry) days ago
                                            }
                                            else
                                            {
                                                @:Photocard expired @Math.Abs(daysUntilPhotocardExpiry) days ago
                                            }
                                        </p>
                                    }
                                    else if (earliestExpiry == 0)
                                    {
                                        <p class="text-danger">
                                            @if (daysUntilExpiry == 0 && daysUntilPhotocardExpiry == 0)
                                            {
                                                @:Both expire today!
                                            }
                                            else if (daysUntilExpiry == 0)
                                            {
                                                @:Licence expires today!
                                            }
                                            else
                                            {
                                                @:Photocard expires today!
                                            }
                                        </p>
                                    }
                                    else
                                    {
                                        <p class="@(earliestExpiry < 180 ? "text-warning" : "text-success")">
                                            @if (earliestExpiry == 1)
                                            {
                                                @:1 day until next expiry
                                            }
                                            else
                                            {
                                                @earliestExpiry @:days until next expiry
                                            }
                                        </p>
                                    }
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-3">
                                <a asp-page="./Edit" asp-route-id="@Model.DrivingLicenceId" class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a asp-page="./Delete" asp-route-id="@Model.DrivingLicenceId" class="btn btn-sm btn-outline-danger">
                                    <i class="bi bi-trash"></i> Delete
                                </a>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
