using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.GpPractices
{
    public class EditModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public EditModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        public int GpPracticeId { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Name is required")]
        [StringLength(50, ErrorMessage = "Name cannot exceed 50 characters")]
        [DisplayName("Practice Name")]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Street Line One is required")]
        [StringLength(100, ErrorMessage = "Street Line One cannot exceed 100 characters")]
        [DisplayName("Street Line One")]
        public string StreetLineOne { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(100, ErrorMessage = "Street Line Two cannot exceed 100 characters")]
        [DisplayName("Street Line Two")]
        public string? StreetLineTwo { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "City is required")]
        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        [DisplayName("City")]
        public string City { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Postcode is required")]
        [StringLength(20, ErrorMessage = "Postcode cannot exceed 20 characters")]
        [DisplayName("Postcode")]
        public string Postcode { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(20, ErrorMessage = "Phone Number cannot exceed 20 characters")]
        [DisplayName("Phone Number")]
        public string? PhoneNumber { get; set; }

        [BindProperty]
        [StringLength(100, ErrorMessage = "Website cannot exceed 100 characters")]
        [DisplayName("Website")]
        public string? Website { get; set; }

        [BindProperty]
        [DisplayName("Address")]
        public int? AddressId { get; set; }

        public SelectList? Addresses { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var gpPractice = await _context.GpPractices
                .Where(g => g.Id == id && g.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (gpPractice == null)
            {
                return NotFound();
            }

            GpPracticeId = gpPractice.Id;
            Name = gpPractice.Name;
            StreetLineOne = gpPractice.StreetLineOne;
            StreetLineTwo = gpPractice.StreetLineTwo;
            City = gpPractice.City;
            Postcode = gpPractice.Postcode;
            PhoneNumber = gpPractice.PhoneNumber;
            Website = gpPractice.Website;
            AddressId = gpPractice.AddressId;

            await LoadAddresses(currentUser.Id);
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            if (!ModelState.IsValid)
            {
                await LoadAddresses(currentUser.Id);
                return Page();
            }

            // Get the existing GP practice
            var existingGpPractice = await _context.GpPractices
                .Where(g => g.Id == GpPracticeId && g.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (existingGpPractice == null)
            {
                return NotFound();
            }

            // Update the GP practice properties
            existingGpPractice.Name = Name;
            existingGpPractice.StreetLineOne = StreetLineOne;
            existingGpPractice.StreetLineTwo = StreetLineTwo;
            existingGpPractice.City = City;
            existingGpPractice.Postcode = Postcode;
            existingGpPractice.PhoneNumber = PhoneNumber;
            existingGpPractice.Website = Website;
            existingGpPractice.AddressId = AddressId;
            existingGpPractice.UpdatedAt = DateTime.UtcNow; // Set UpdatedAt to current time

            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "GP practice updated successfully";

            return RedirectToPage("./Details", new { id = GpPracticeId });
        }

        private async Task LoadAddresses(string userId)
        {
            var addresses = await _context.Addresses
                .AsNoTracking()
                .Where(a => a.UserId == userId)
                .OrderBy(a => a.HouseFlatNumber)
                .ThenBy(a => a.StreetLineOne)
                .Select(a => new { a.Id, Display = $"{a.HouseFlatNumber} {a.StreetLineOne}, {a.City}, {a.Postcode}" })
                .ToListAsync();

            Addresses = new SelectList(addresses, "Id", "Display", AddressId);
        }

        private bool GpPracticeExists(int id)
        {
            return _context.GpPractices.Any(e => e.Id == id);
        }
    }
}
