@page
@model Life.Pages.GpPractices.DetailsModel
@{
    ViewData["Title"] = "GP Practice Details";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">GP Practice Details</h2>
                </div>
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
					</div>
                }
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4 class="mb-3">Practice Information</h4>
                            
                            <div class="mb-3">
                                <strong>Practice Name:</strong>
                                <p class="mb-1">@Model.Name</p>
                            </div>

                            <div class="mb-3">
                                <strong>Address:</strong>
                                <p class="mb-1">@Model.StreetLineOne</p>
                                @if (!string.IsNullOrEmpty(Model.StreetLineTwo))
                                {
                                    <p class="mb-1">@Model.StreetLineTwo</p>
                                }
                                <p class="mb-1">@Model.City</p>
                                <p class="mb-1">@Model.Postcode</p>
                            </div>

                            @if (!string.IsNullOrEmpty(Model.PhoneNumber))
                            {
                                <div class="mb-3">
                                    <strong>Phone Number:</strong>
                                    <p class="mb-1">@Model.PhoneNumber</p>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(Model.Website))
                            {
                                <div class="mb-3">
                                    <strong>Website:</strong>
                                    <p class="mb-1">
                                        @if (Model.Website.StartsWith("http"))
                                        {
                                            <a href="@Model.Website" target="_blank">@Model.Website</a>
                                        }
                                        else
                                        {
                                            <a href="https://@Model.Website" target="_blank">@Model.Website</a>
                                        }
                                    </p>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(Model.AddressDisplay))
                            {
                                <div class="mb-3">
                                    <strong>Linked Address:</strong>
                                    <p class="mb-1">
                                        <a asp-page="/Addresses/Details" asp-route-id="@Model.AddressId">@Model.AddressDisplay</a>
                                    </p>
                                </div>
                            }
                        </div>

                        <div class="col-md-6">
                            <h4 class="mb-3">Record Information</h4>
                            
                            <div class="mb-3">
                                <strong>Created:</strong>
                                <p class="mb-1">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</p>
                            </div>

                            <div class="mb-3">
                                <strong>Last Updated:</strong>
                                <p class="mb-1">@Model.UpdatedAt.ToString("dd/MM/yyyy HH:mm")</p>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <a asp-page="./Edit" asp-route-id="@Model.GpPracticeId" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil"></i> Edit
                            </a>
                            <a asp-page="./Delete" asp-route-id="@Model.GpPracticeId" class="btn btn-sm btn-danger">
                                <i class="bi bi-trash"></i> Delete
                            </a>
                            <a asp-page="./Index" class="btn btn-sm btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
