using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.DrivingLicences
{
    public class DetailsModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DetailsModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public int DrivingLicenceId { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateOnly StartDate { get; set; }
        public DateOnly ExpiryDate { get; set; }
        public DateOnly PhotocardStartDate { get; set; }
        public DateOnly PhotocardExpiryDate { get; set; }
        public string? LicenceNumber { get; set; }
        public string? Notes { get; set; }
        public int? AddressId { get; set; }
        public string? AddressDisplay { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public bool HasLinkedReminders { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var drivingLicence = await _context.DrivingLicences
                .AsNoTracking()
                .Include(d => d.Address)
                .Where(d => d.Id == id && d.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (drivingLicence == null)
            {
                return NotFound();
            }

            DrivingLicenceId = drivingLicence.Id;
            Name = drivingLicence.Name;
            StartDate = drivingLicence.StartDate;
            ExpiryDate = drivingLicence.ExpiryDate;
            PhotocardStartDate = drivingLicence.PhotocardStartDate;
            PhotocardExpiryDate = drivingLicence.PhotocardExpiryDate;
            LicenceNumber = drivingLicence.LicenceNumber;
            Notes = drivingLicence.Notes;
            AddressId = drivingLicence.AddressId;
            CreatedAt = drivingLicence.CreatedAt;
            UpdatedAt = drivingLicence.UpdatedAt;

            // Format address display
            if (drivingLicence.Address != null)
            {
                AddressId = drivingLicence.Address.Id;
                AddressDisplay = $"{drivingLicence.Address.HouseFlatNumber} {drivingLicence.Address.StreetLineOne}";
                if (!string.IsNullOrEmpty(drivingLicence.Address.StreetLineTwo))
                {
                    AddressDisplay += $", {drivingLicence.Address.StreetLineTwo}";
                }
                AddressDisplay += $", {drivingLicence.Address.City}, {drivingLicence.Address.Postcode}";
            }

            // Check if there are linked automatic reminders
            HasLinkedReminders = await _context.Reminders
                .AnyAsync(r => r.DrivingLicenceId == drivingLicence.Id && r.IsAutomatic == true);

            return Page();
        }
    }
}
