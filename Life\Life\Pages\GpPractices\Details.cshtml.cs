using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.GpPractices
{
    public class DetailsModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public DetailsModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public int GpPracticeId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string StreetLineOne { get; set; } = string.Empty;
        public string? StreetLineTwo { get; set; }
        public string City { get; set; } = string.Empty;
        public string Postcode { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? Website { get; set; }
        public int? AddressId { get; set; }
        public string? AddressDisplay { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            var gpPractice = await _context.GpPractices
                .Include(g => g.Address)
                .Where(g => g.Id == id && g.UserId == currentUser.Id)
                .FirstOrDefaultAsync();

            if (gpPractice == null)
            {
                return NotFound();
            }

            GpPracticeId = gpPractice.Id;
            Name = gpPractice.Name;
            StreetLineOne = gpPractice.StreetLineOne;
            StreetLineTwo = gpPractice.StreetLineTwo;
            City = gpPractice.City;
            Postcode = gpPractice.Postcode;
            PhoneNumber = gpPractice.PhoneNumber;
            Website = gpPractice.Website;
            AddressId = gpPractice.AddressId;
            CreatedAt = gpPractice.CreatedAt;
            UpdatedAt = gpPractice.UpdatedAt;

            // Format address display
            if (gpPractice.Address != null)
            {
                AddressId = gpPractice.Address.Id;
                AddressDisplay = $"{gpPractice.Address.HouseFlatNumber} {gpPractice.Address.StreetLineOne}";
                if (!string.IsNullOrEmpty(gpPractice.Address.StreetLineTwo))
                {
                    AddressDisplay += $", {gpPractice.Address.StreetLineTwo}";
                }
                AddressDisplay += $", {gpPractice.Address.City}, {gpPractice.Address.Postcode}";
            }

            return Page();
        }
    }
}
