using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace Life.Pages.GpPractices
{
    public class IndexModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public IndexModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public IList<GpPractice> GpPractices { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();
            
            GpPractices = await _context.GpPractices
                .AsNoTracking()
                .Where(g => g.UserId == currentUser.Id)
                .OrderBy(g => g.Name)
                .ToListAsync();

            return Page();
        }
    }
}
