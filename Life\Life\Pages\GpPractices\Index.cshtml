@page
@model Life.Pages.GpPractices.IndexModel
@{
    ViewData["Title"] = "GP Practices";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">GP Practices</h2>
                    <a asp-page="./Create" class="btn btn-sm btn-outline-success title-button">
                        <i class="bi bi-plus-square"></i>
                    </a>
                </div>
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="col-12 d-flex flex-column align-items-center justify-content-center">
                        <div class="alert alert-success alert-dismissible fade show my-3" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
					</div>
                }

                @if (Model.GpPractices.Any())
                {
                    <div class="card-body">
                        <h5 class="card-title">Current Driving Licences</h5>

                        <div class="list-group">
                            @foreach (var practice in Model.GpPractices)
                            {
                                <div class="list-group-item py-3">
                                    <div class="d-flex justify-content-between my-3">
                                        <div>
                                            <h5 class="mb-2 text-break">@practice.Name</h5>
                                            <h6>@practice.StreetLineOne@(!string.IsNullOrEmpty(practice.StreetLineTwo) ? ", " + practice.StreetLineTwo : "")</h6>
                                            <h6>@practice.City, @practice.Postcode</h6>
                                            @if (!string.IsNullOrEmpty(practice.PhoneNumber))
                                            {
                                                <h6>Phone: @practice.PhoneNumber</h6>
                                            }
                                            <div class="mt-2">
                                                <a asp-page="./Details" asp-route-id="@practice.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> Details
                                                </a>
                                                <a asp-page="./Edit" asp-route-id="@practice.Id" class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
                else
                {
                    <div class="card-body">
                        <p class="mt-3">There are no GP practices</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
