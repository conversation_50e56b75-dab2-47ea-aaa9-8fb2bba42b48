using Life.Data;
using Life.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Life.Pages.GpPractices
{
    public class CreateModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public CreateModel(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        [BindProperty]
        [Required(ErrorMessage = "Name is required")]
        [StringLength(50, ErrorMessage = "Name cannot exceed 50 characters")]
        [DisplayName("Practice Name")]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Street Line One is required")]
        [StringLength(100, ErrorMessage = "Street Line One cannot exceed 100 characters")]
        [DisplayName("Street Line One")]
        public string StreetLineOne { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(100, ErrorMessage = "Street Line Two cannot exceed 100 characters")]
        [DisplayName("Street Line Two")]
        public string? StreetLineTwo { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "City is required")]
        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        [DisplayName("City")]
        public string City { get; set; } = string.Empty;

        [BindProperty]
        [Required(ErrorMessage = "Postcode is required")]
        [StringLength(20, ErrorMessage = "Postcode cannot exceed 20 characters")]
        [DisplayName("Postcode")]
        public string Postcode { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(20, ErrorMessage = "Phone Number cannot exceed 20 characters")]
        [DisplayName("Phone Number")]
        public string? PhoneNumber { get; set; }

        [BindProperty]
        [StringLength(100, ErrorMessage = "Website cannot exceed 100 characters")]
        [DisplayName("Website")]
        public string? Website { get; set; }

        [BindProperty]
        [DisplayName("Address")]
        public int? AddressId { get; set; }

        public SelectList? Addresses { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            await LoadAddresses(currentUser.Id);
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser == null)
                return Challenge();

            if (!ModelState.IsValid)
            {
                await LoadAddresses(currentUser.Id);
                return Page();
            }

            // Create the GP practice
            var gpPractice = new GpPractice
            {
                Name = Name,
                StreetLineOne = StreetLineOne,
                StreetLineTwo = StreetLineTwo,
                City = City,
                Postcode = Postcode,
                PhoneNumber = PhoneNumber,
                Website = Website,
                AddressId = AddressId,
                User = currentUser,
                UserId = currentUser.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.GpPractices.Add(gpPractice);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "GP practice created successfully";

            return RedirectToPage("./Details", new { id = gpPractice.Id });
        }

        private async Task LoadAddresses(string userId)
        {
            var addresses = await _context.Addresses
                .AsNoTracking()
                .Where(a => a.UserId == userId)
                .OrderBy(a => a.HouseFlatNumber)
                .ThenBy(a => a.StreetLineOne)
                .Select(a => new { a.Id, Display = $"{a.HouseFlatNumber} {a.StreetLineOne}, {a.City}, {a.Postcode}" })
                .ToListAsync();

            Addresses = new SelectList(addresses, "Id", "Display", AddressId);
        }
    }
}
